import React, { useEffect, useState, useCallback } from 'react';
import { useSignalR } from '../hooks/useSignalR';
import { RoomStatusChangeNotification } from '../services/signalr-service';

interface Room {
  id: string;
  roomNumber: string;
  roomCode: string;
  status: {
    id: string;
    name: string;
    color: string;
    code: string;
  };
  lastUpdated: string;
}

interface RoomStatusDashboardProps {
  apiBaseUrl: string;
  getAccessToken: () => Promise<string | null>;
}

export const RoomStatusDashboard: React.FC<RoomStatusDashboardProps> = ({
  apiBaseUrl,
  getAccessToken,
}) => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [notifications, setNotifications] = useState<RoomStatusChangeNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const signalRHubUrl = `${apiBaseUrl}/signalr/roomstatus`;

  const {
    connectionState,
    isConnected,
    connect,
    disconnect,
    onRoomStatusChanged,
    offRoomStatusChanged,
  } = useSignalR({
    hubUrl: signalRHubUrl,
    getAccessToken,
    autoConnect: true,
  });

  // Handle room status change notifications
  const handleRoomStatusChanged = useCallback((notification: RoomStatusChangeNotification) => {
    console.log('Room status changed:', notification);

    // Update the room in the list
    setRooms(prevRooms => 
      prevRooms.map(room => 
        room.id === notification.roomId
          ? {
              ...room,
              status: notification.newStatus,
              lastUpdated: notification.changeTimestamp,
            }
          : room
      )
    );

    // Add notification to the list (keep only last 10)
    setNotifications(prevNotifications => [
      notification,
      ...prevNotifications.slice(0, 9)
    ]);

    // Show browser notification if supported
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Room Status Updated', {
        body: `Room ${notification.roomNumber} status changed to ${notification.newStatus.name}`,
        icon: '/favicon.ico',
      });
    }
  }, []);

  // Set up SignalR event listeners
  useEffect(() => {
    onRoomStatusChanged(handleRoomStatusChanged);

    return () => {
      offRoomStatusChanged(handleRoomStatusChanged);
    };
  }, [handleRoomStatusChanged, onRoomStatusChanged, offRoomStatusChanged]);

  // Note: We don't need to join individual room groups for a dashboard
  // The SignalR hub automatically adds all users to "RoomStatusUpdates" group
  // which receives notifications for ALL room status changes

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Fetch initial room data
  useEffect(() => {
    const fetchRooms = async () => {
      try {
        setLoading(true);
        const token = await getAccessToken();
        
        const response = await fetch(`${apiBaseUrl}/api/app/rooms`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch rooms: ${response.statusText}`);
        }

        const data = await response.json();
        
        // Transform API data to our Room interface
        const transformedRooms: Room[] = data.items?.map((item: any) => ({
          id: item.id,
          roomNumber: item.roomNumber,
          roomCode: item.roomCode,
          status: item.roomStatus,
          lastUpdated: item.lastModificationTime || item.creationTime,
        })) || [];

        setRooms(transformedRooms);
        setError(null);
      } catch (err) {
        console.error('Error fetching rooms:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch rooms');
      } finally {
        setLoading(false);
      }
    };

    fetchRooms();
  }, [apiBaseUrl, getAccessToken]);

  const getStatusColor = (status: Room['status']) => {
    return status.color || '#gray';
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading rooms...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <strong>Error:</strong> {error}
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Room Status Dashboard</h1>
        <div className="flex items-center space-x-4">
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            isConnected 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            SignalR: {connectionState}
          </div>
          <button
            onClick={isConnected ? disconnect : connect}
            className={`px-4 py-2 rounded text-sm font-medium ${
              isConnected
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'bg-green-500 hover:bg-green-600 text-white'
            }`}
          >
            {isConnected ? 'Disconnect' : 'Connect'}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Room Grid */}
        <div className="lg:col-span-2">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Rooms</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {rooms.map((room) => (
              <div
                key={room.id}
                className="bg-white rounded-lg shadow-md p-4 border-l-4"
                style={{ borderLeftColor: getStatusColor(room.status) }}
              >
                <div className="font-semibold text-gray-900">{room.roomNumber}</div>
                <div className="text-sm text-gray-600">{room.roomCode}</div>
                <div 
                  className="mt-2 px-2 py-1 rounded text-xs font-medium text-white"
                  style={{ backgroundColor: getStatusColor(room.status) }}
                >
                  {room.status.name}
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  Updated: {formatTimestamp(room.lastUpdated)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Notifications Panel */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Updates</h2>
          <div className="bg-white rounded-lg shadow-md max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-4 text-gray-500 text-center">
                No recent updates
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {notifications.map((notification, index) => (
                  <div key={index} className="p-4">
                    <div className="font-medium text-gray-900">
                      Room {notification.roomNumber}
                    </div>
                    <div className="text-sm text-gray-600">
                      {notification.previousStatus?.name || 'Unknown'} → {notification.newStatus.name}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {formatTimestamp(notification.changeTimestamp)}
                    </div>
                    <div className="text-xs text-gray-400">
                      {notification.statusSource}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
