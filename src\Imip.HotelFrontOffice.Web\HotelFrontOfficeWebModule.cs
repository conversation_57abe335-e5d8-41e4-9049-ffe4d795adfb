using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using HealthChecks.UI.Client;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Attachments.BackgroundWorkers;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Imip.HotelFrontOffice.Localization;
using Imip.HotelFrontOffice.MultiTenancy;
using Imip.HotelFrontOffice.Users;
using Imip.HotelFrontOffice.Web.Authorization;
using Imip.HotelFrontOffice.Web.Menus;
using Imip.HotelFrontOffice.Web.Middleware;
using Imip.HotelFrontOffice.Web.Swagger;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
// We're disabling OpenIddict validation and using only JWT Bearer authentication
using OpenIddict.Server.AspNetCore;
using TickerQ.Dashboard.DependencyInjection;
using TickerQ.DependencyInjection;
using TickerQ.EntityFrameworkCore.DependencyInjection;
using Volo.Abp;
using Volo.Abp.AspNetCore.Authentication.JwtBearer;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.Libs;
using Volo.Abp.AspNetCore.Mvc.Localization;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.AspNetCore.SignalR;
using Volo.Abp.Autofac;
using Volo.Abp.AutoMapper;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity.Web;
using Volo.Abp.Json;
using Volo.Abp.Json.SystemTextJson;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict;
using Volo.Abp.PermissionManagement;
using Volo.Abp.Security.Claims;
using Volo.Abp.Studio.Client.AspNetCore;
using Volo.Abp.Swashbuckle;
using Volo.Abp.TenantManagement.Web;
using Volo.Abp.UI.Navigation;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.Validation.Localization;
using Volo.Abp.VirtualFileSystem;

namespace Imip.HotelFrontOffice.Web;

[DependsOn(
    typeof(HotelFrontOfficeHttpApiModule),
    typeof(HotelFrontOfficeApplicationModule),
    typeof(HotelFrontOfficeEntityFrameworkCoreModule),
    typeof(AbpAutofacModule),
    typeof(AbpStudioClientAspNetCoreModule),
    typeof(AbpIdentityWebModule),
    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpTenantManagementWebModule),
    typeof(AbpFeatureManagementWebModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpAspNetCoreAuthenticationJwtBearerModule),
    typeof(AbpAspNetCoreSignalRModule)
)]
public class HotelFrontOfficeWebModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        // Configure OpenIddict validation
        PreConfigure<OpenIddictBuilder>(builder =>
        {
            builder.AddValidation(options =>
            {
                options.AddAudiences("HotelFrontOffice");
                options.UseAspNetCore();
                options.UseLocalServer();
                options.SetIssuer(configuration["AuthServer:Authority"]!);
            });
        });

        context.Services.PreConfigure<AbpMvcDataAnnotationsLocalizationOptions>(options =>
        {
            options.AddAssemblyResource(
                typeof(HotelFrontOfficeResource),
                typeof(HotelFrontOfficeDomainModule).Assembly,
                typeof(HotelFrontOfficeDomainSharedModule).Assembly,
                typeof(HotelFrontOfficeApplicationModule).Assembly,
                typeof(HotelFrontOfficeApplicationContractsModule).Assembly,
                typeof(HotelFrontOfficeWebModule).Assembly
            );
        });

        // This section is commented out as we're using the configuration above
        // PreConfigure<OpenIddictBuilder>(builder =>
        // {
        //     builder.AddValidation(options =>
        //     {
        //         options.AddAudiences("HotelFrontOffice");
        //         options.UseLocalServer();
        //         options.UseAspNetCore();
        //         options.SetIssuer(configuration["AuthServer:Authority"]!);
        //
        //         // For development environments, we'll handle HTTPS requirements in the JWT Bearer options
        //         // and in the custom middleware
        //     });
        // });

        if (!hostingEnvironment.IsDevelopment())
        {
            PreConfigure<AbpOpenIddictAspNetCoreOptions>(options =>
            {
                options.AddDevelopmentEncryptionAndSigningCertificate = false;
            });

            PreConfigure<OpenIddictServerBuilder>(serverBuilder =>
            {
                serverBuilder.AddProductionEncryptionAndSigningCertificate("openiddict.pfx",
                    configuration["AuthServer:CertificatePassPhrase"]!);
                serverBuilder.SetIssuer(new Uri(configuration["AuthServer:Authority"]!));
            });
        }
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        // Disable client-side library check for containers
        Configure<AbpMvcLibsOptions>(options => { options.CheckLibs = false; });

        if (!configuration.GetValue<bool>("App:DisablePII"))
        {
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.LogCompleteSecurityArtifact = true;
        }


        if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata"))
        {
            Configure<OpenIddictServerAspNetCoreOptions>(options =>
            {
                options.DisableTransportSecurityRequirement = true;
            });

            Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedProto;
            });
        }

        // Configure localization services
        ConfigureLocalization(context.Services);

        // Configure HTTP clients
        ConfigureHttpClients(context.Services, configuration);

        ConfigureBundles();
        ConfigureUrls(configuration);
        ConfigureAuthentication(context);
        ConfigureCors(context.Services, configuration);
        ConfigureAutoMapper();
        ConfigureVirtualFileSystem(hostingEnvironment);
        ConfigureNavigationServices();
        ConfigureAutoApiControllers();
        ConfigureSwaggerServices(context.Services);
        ConfigureHealthChecks(context.Services);

        // Register background workers
        context.Services.AddTransient<TemporaryZipFileCleanupWorker>();
        // context.Services.AddTransient<EntityChangeLogs.EntityChangeTrackingWorker>();

        // Configure TickerQ Dashboard
        context.Services.AddTickerQ(options =>
        {
            options.AddOperationalStore<HotelFrontOfficeDbContext>();
            options.SetInstanceIdentifier("HotelFrontOffice");
            options.CancelMissedTickersOnApplicationRestart();

            // Enable Dashboard
            options.AddDashboard(basePath: "/tickerq-dashboard");
            options.AddDashboardBasicAuth();
        });

        // Configure System.Text.Json to handle circular references
        // context.Services.AddControllers()
        //     .AddJsonOptions(options =>
        //     {
        //         options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.Preserve;
        //         options.JsonSerializerOptions.MaxDepth = 64; // Increase max depth from default 32
        //     });

        // // Configure ABP JSON serialization options
        // Configure<AbpSystemTextJsonSerializerOptions>(options =>
        // {
        //     options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.Preserve;
        //     options.JsonSerializerOptions.MaxDepth = 64; // Increase max depth from default 32
        // });

        // Enable dynamic permission management
        Configure<PermissionManagementOptions>(options => { options.IsDynamicPermissionStoreEnabled = true; });

        // Configure user synchronization options
        Configure<UserSynchronizationOptions>(options =>
        {
            options.IsEnabled = configuration.GetValue<bool>("UserSynchronization:IsEnabled", true);
            options.UpdateExistingUsers = configuration.GetValue<bool>("UserSynchronization:UpdateExistingUsers", true);
            options.SynchronizeRoles = configuration.GetValue<bool>("UserSynchronization:SynchronizeRoles", true);
            options.SynchronizeClaims = configuration.GetValue<bool>("UserSynchronization:SynchronizeClaims", true);
            options.EnableLogging = configuration.GetValue<bool>("UserSynchronization:EnableLogging", true);
        });

        // Configure Identity options to allow spaces and special characters in usernames
        Configure<IdentityOptions>(options =>
        {
            // Allow spaces and special characters in usernames for external user synchronization
            options.User.AllowedUserNameCharacters =
                "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+ ";

            // Relax other validation requirements for external users
            options.User.RequireUniqueEmail = false; // Email might not always be unique in external systems

            // Password requirements (not used for external users but required for Identity)
            options.Password.RequireDigit = true;
            options.Password.RequiredLength = 8;
            options.Password.RequireNonAlphanumeric = true;
            options.Password.RequireUppercase = true;
            options.Password.RequireLowercase = true;

            // Lockout settings
            options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(30);
            options.Lockout.MaxFailedAccessAttempts = 5;
            options.Lockout.AllowedForNewUsers = true;

            // Sign-in settings
            options.SignIn.RequireConfirmedEmail = false;
            options.SignIn.RequireConfirmedPhoneNumber = false;
        });

        // Configure permission definition providers
        // We don't need to explicitly register WismaAppPermissionDefinitionProvider here
        // as it's already registered through the module system
        // Configure<AbpPermissionOptions>(options =>
        // {
        //     options.DefinitionProviders.Add<WismaAppPermissionDefinitionProvider>();
        // });
    }


    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                LeptonXLiteThemeBundles.Styles.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-scripts.js");
                    bundle.AddFiles("/global-styles.css");
                }
            );
        });
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options => { options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"]; });
    }

    private void ConfigureAuthentication(ServiceConfigurationContext context)
    {
        var configuration = context.Services.GetConfiguration();
        var hostingEnvironment = context.Services.GetHostingEnvironment();

        // Configure authentication with JWT Bearer only
        context.Services.AddAuthentication(options =>
            {
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.Authority = configuration["AuthServer:Authority"];
                options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata");
                options.Audience = "IdentityServer";

                options.TokenValidationParameters = new TokenValidationParameters
                {
                    // Always validate lifetime regardless of environment
                    ValidateLifetime = true,

                    // For development, we can disable other validations
                    ValidateIssuer = hostingEnvironment.IsDevelopment() ? false : true,
                    ValidateAudience = hostingEnvironment.IsDevelopment() ? false : true,
                    // Disable signature validation in development
                    ValidateIssuerSigningKey = hostingEnvironment.IsDevelopment() ? false : true,

                    // These values are still used in production
                    ValidIssuer = configuration["AuthServer:Authority"],
                    ValidAudience = "IdentityServer",
                    ClockSkew = TimeSpan.FromMinutes(5),

                    // Always require expiration time
                    RequireExpirationTime = true,
                    // Don't require signed tokens in development
                    RequireSignedTokens = hostingEnvironment.IsDevelopment() ? false : true,

                    // Map standard claims
                    NameClaimType = "name",
                    RoleClaimType = "role"
                };

                // Handle the case where the identity server might not be available
                options.BackchannelHttpHandler = new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback =
                        HttpClientHandler.DangerousAcceptAnyServerCertificateValidator
                };

                // Set a reasonable timeout for metadata requests
                options.MetadataAddress = configuration["AuthServer:Authority"] + "/.well-known/openid-configuration";
                options.BackchannelTimeout = TimeSpan.FromSeconds(30);

                // Skip metadata validation in development
                if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata"))
                {
                    options.RequireHttpsMetadata = false;
                }

                options.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        // Log the authentication error
                        Console.WriteLine($"Authentication failed: {context.Exception.Message}");

                        // In development mode, we'll allow any authentication errors
                        if (hostingEnvironment.IsDevelopment())
                        {
                            // Add headers for debugging
                            if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
                            {
                                context.Response.Headers["Token-Expired"] = "true";
                            }
                            else if (context.Exception.Message.Contains("IDX10503") ||
                                     context.Exception.Message.Contains("Signature validation failed"))
                            {
                                context.Response.Headers["Token-Signature-Invalid"] = "true";
                            }

                            // Set NoResult to suppress the 401 challenge response
                            context.NoResult();

                            // Create a minimal claims identity with the necessary claims
                            var claims = new List<Claim>();

                            // Try to extract claims from the token if possible
                            try
                            {
                                var token = context.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                                var handler = new JwtSecurityTokenHandler();
                                var jwtToken = handler.ReadJwtToken(token);

                                // Extract claims from the token
                                claims = jwtToken.Claims.ToList();
                            }
                            catch
                            {
                                // If we can't extract claims, add some minimal claims
                                claims.Add(new Claim(ClaimTypes.Name, "DevUser"));
                                claims.Add(new Claim(ClaimTypes.Role, "admin"));
                            }

                            // Create a new ClaimsIdentity
                            var identity = new ClaimsIdentity(claims, "Development");
                            var principal = new ClaimsPrincipal(identity);

                            // Set the principal
                            context.Principal = principal;

                            // Continue processing the request
                            context.Success();
                        }
                        else
                        {
                            // In production, return a proper JSON error response
                            context.NoResult();

                            // Set the response status code to 401 Unauthorized
                            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                            context.Response.ContentType = "application/json";

                            // Create a JSON error response
                            var errorResponse = new
                            {
                                error = new
                                {
                                    code = "Unauthorized",
                                    message = context.Exception is SecurityTokenExpiredException
                                        ? "Your authentication token has expired"
                                        : "Invalid authentication token",
                                    details = context.Exception.Message
                                }
                            };

                            // Serialize the error response to JSON
                            var jsonResponse = JsonSerializer.Serialize(errorResponse);

                            // Write the JSON response to the response body
                            return context.Response.WriteAsync(jsonResponse);
                        }

                        return Task.CompletedTask;
                    },
                    OnTokenValidated = context => { return Task.CompletedTask; },
                    OnMessageReceived = context =>
                    {
                        Console.WriteLine("JWT token received");
                        return Task.CompletedTask;
                    }
                };
            });

        // Forward identity authentication for bearer tokens
        context.Services.ForwardIdentityAuthenticationForBearer(JwtBearerDefaults.AuthenticationScheme);

        // Configure authorization
        context.Services.AddAuthorization();

        // Replace the default policy provider with our dynamic policy provider
        context.Services.AddSingleton<IAuthorizationPolicyProvider, DynamicAuthorizationPolicyProvider>();

        // Disable dynamic claims resolution to prevent local user lookup
        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = false;
        });

        // Register the authorization handler
        context.Services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();

        // Register our custom middlewares
        context.Services.AddTransient<AuthorizationResponseMiddleware>();
        context.Services.AddTransient<TokenExpirationMiddleware>();

        // Register optimized middleware for better performance
        context.Services.AddTransient<OptimizedTokenValidationMiddleware>();
        context.Services.AddTransient<OptimizedUserSynchronizationMiddleware>();
    }

    private void ConfigureCors(IServiceCollection services, IConfiguration configuration)
    {
        services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder
                    .WithOrigins(
                        "http://localhost:3000",
                        "https://localhost:3000",
                        "http://localhost:3001",
                        "https://localhost:3001",
                        "https://wisma-dev.imip.co.id",
                        "https://wisma.imip.co.id",
                        configuration["App:SelfUrl"] ?? "https://localhost:44357"
                    )
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials(); // Required for SignalR
            });
        });
    }

    private void ConfigureAutoMapper()
    {
        Configure<AbpAutoMapperOptions>(options => { options.AddMaps<HotelFrontOfficeWebModule>(); });
    }

    private void ConfigureVirtualFileSystem(IWebHostEnvironment hostingEnvironment)
    {
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<HotelFrontOfficeWebModule>();

            // Only replace embedded resources with physical files in a development environment,
            // and only if we're not running in a container
            if (hostingEnvironment.IsDevelopment() && !IsRunningInContainer())
            {
                try
                {
                    var domainSharedPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.IdentityServer.Domain.Shared", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainSharedPath))
                    {
                        options.FileSets
                            .ReplaceEmbeddedByPhysical<HotelFrontOfficeDomainSharedModule>(domainSharedPath);
                    }

                    var domainPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.IdentityServer.Domain", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<HotelFrontOfficeDomainModule>(domainPath);
                    }

                    var contractsPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.IdentityServer.Application.Contracts", Path.DirectorySeparatorChar));
                    if (Directory.Exists(contractsPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<HotelFrontOfficeApplicationContractsModule>(
                            contractsPath);
                    }

                    var appPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.IdentityServer.Application", Path.DirectorySeparatorChar));
                    if (Directory.Exists(appPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<HotelFrontOfficeApplicationModule>(appPath);
                    }

                    var httpApiPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}..{0}src{0}Imip.IdentityServer.HttpApi", Path.DirectorySeparatorChar));
                    if (Directory.Exists(httpApiPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<HotelFrontOfficeHttpApiModule>(httpApiPath);
                    }

                    if (Directory.Exists(hostingEnvironment.ContentRootPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<HotelFrontOfficeWebModule>(hostingEnvironment
                            .ContentRootPath);
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception but continue without replacing embedded resources
                    Console.WriteLine($"Error configuring virtual file system: {ex.Message}");
                }
            }
        });
    }

    private bool IsRunningInContainer()
    {
        // Check for container-specific environment indicators
        return File.Exists("/.dockerenv") ||
               (File.Exists("/proc/1/cgroup") && File.ReadAllText("/proc/1/cgroup").Contains("/docker/"));
    }

    private void ConfigureNavigationServices()
    {
        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new HotelFrontOfficeMenuContributor());
        });

        Configure<AbpToolbarOptions>(options =>
        {
            options.Contributors.Add(new HotelFrontOfficeToolbarContributor());
        });
    }

    private void ConfigureAutoApiControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(HotelFrontOfficeApplicationModule).Assembly);
        });

        // Add our custom file validation exception filter and model binder provider
        Configure<MvcOptions>(options =>
        {
            options.Filters.Add<Filters.FileValidationExceptionFilter>();
        });
    }

    private void ConfigureSwaggerServices(IServiceCollection services)
    {
        services.AddAbpSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "IdentityServer API", Version = "v1" });

                // Configure Swagger to handle file uploads with IFormFile
                options.OperationFilter<SwaggerFileOperationFilter>();

                // Add support for multipart/form-data
                options.MapType<IFormFile>(() => new OpenApiSchema
                {
                    Type = "string",
                    Format = "binary"
                });

                // Add support for FileUploadFormDto
                options.MapType<FileUploadFormDto>(() => new OpenApiSchema
                {
                    Type = "object",
                    Properties = new Dictionary<string, OpenApiSchema>
                    {
                        ["File"] = new OpenApiSchema { Type = "string", Format = "binary" },
                        ["Description"] = new OpenApiSchema { Type = "string" },
                        ["ReferenceId"] = new OpenApiSchema { Type = "string", Format = "uuid" },
                        ["ReferenceType"] = new OpenApiSchema { Type = "string" }
                    },
                    Required = new HashSet<string> { "File" }
                });

                options.DocInclusionPredicate((docName, description) =>
                {
                    // Check if the controller name contains "Abp" (for ABP framework controllers)
                    if (description.ActionDescriptor != null &&
                        description.ActionDescriptor.RouteValues != null &&
                        description.ActionDescriptor.RouteValues.TryGetValue("controller", out var controllerName) &&
                        controllerName != null &&
                        (controllerName.Contains("Abp") ||
                         controllerName.StartsWith("Abp")))
                    {
                        if (description.RelativePath != null &&
                            (description.RelativePath.Contains("application-configuration") ||
                             description.RelativePath.Contains("api/abp/application-configuration")))
                        {
                            return true; // Exclude application-configuration API
                        }

                        if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/tenant") ||
                         description.RelativePath.Contains("/tenants")))
                        {
                            return true; // Exclude tenant-related APIs
                        }

                        // Check if the relative path contains "account" or "accounts"
                        if (description.RelativePath != null &&
                            (description.RelativePath.Contains("/account") ||
                             description.RelativePath.Contains("/accounts")))
                        {
                            return true; // Exclude account-related APIs
                        }

                        // Exclude ABP framework controllers
                        return false;
                    }


                    // Check if the API is from the Identity module
                    if (description.GroupName != null && description.GroupName.Contains("Identity"))
                    {
                        return false; // Exclude identity management APIs
                    }

                    if (description.GroupName != null && description.GroupName.Contains("PermissionManagement"))
                    {
                        return false; // Exclude permission management APIs
                    }

                    if (description.GroupName != null && description.GroupName.Contains("FeatureManagement"))
                    {
                        return false; // Exclude feature management APIs
                    }

                    if (description.GroupName != null && description.GroupName.Contains("SettingManagement"))
                    {
                        return false; // Exclude feature management APIs
                    }

                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/permission") ||
                         description.RelativePath.Contains("/permissions")))
                    {
                        return false; // Exclude account-related APIs
                    }

                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/feature") ||
                         description.RelativePath.Contains("/features")))
                    {
                        return false; // Exclude feature-related APIs
                    }

                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/setting") ||
                         description.RelativePath.Contains("/settings")))
                    {
                        return false; // Exclude feature-related APIs
                    }

                    // Check if the relative path contains identity-related endpoints
                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/identity") ||
                         description.RelativePath.Contains("/identities") ||
                         description.RelativePath.Contains("/users") ||
                         description.RelativePath.Contains("/roles")))
                    {
                        return false; // Exclude identity-related APIs
                    }

                    // Check if the API is from the AbpApiDefinition controller
                    if (description.ActionDescriptor != null &&
                        description.ActionDescriptor.DisplayName != null &&
                        description.ActionDescriptor.DisplayName.Contains("AbpApiDefinition"))
                    {
                        return false; // Exclude AbpApiDefinition API
                    }

                    // Check if the API is from the AbpApplicationConfiguration controller
                    //    if (description.ActionDescriptor != null &&
                    //        description.ActionDescriptor.DisplayName != null &&
                    //        description.ActionDescriptor.DisplayName.Contains("AbpApplicationConfiguration"))
                    //    {
                    //        return false; // Exclude AbpApplicationConfiguration API
                    //    }

                    // Check if the API is from the AbpApplicationLocalization controller
                    if (description.ActionDescriptor != null &&
                        description.ActionDescriptor.DisplayName != null &&
                        description.ActionDescriptor.DisplayName.Contains("AbpApplicationLocalization"))
                    {
                        return false; // Exclude AbpApplicationLocalization API
                    }

                    // Check if the relative path contains application-configuration
                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("api-definition") ||
                         description.RelativePath.Contains("api/abp/api-definition")))
                    {
                        return false; // Exclude api-definition API
                    }

                    // Check if the relative path contains application-configuration
                    //    if (description.RelativePath != null &&
                    //        (description.RelativePath.Contains("application-configuration") ||
                    //         description.RelativePath.Contains("api/abp/application-configuration")))
                    //    {
                    //        return false; // Exclude application-configuration API
                    //    }

                    // Check if the relative path contains application-localization
                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("application-localization") ||
                         description.RelativePath.Contains("api/abp/application-localization")))
                    {
                        return false; // Exclude application-localization API
                    }

                    return true; // Include all other APIs
                });

                options.CustomSchemaIds(type =>
                {
                    // Handle generic types
                    if (type.IsGenericType)
                    {
                        var prefix = type.Name.Split('`')[0];
                        var genericArgs = string.Join("And", type.GetGenericArguments().Select(t =>
                        {
                            if (t.IsGenericType)
                            {
                                var nestedPrefix = t.Name.Split('`')[0];
                                var nestedArgs = string.Join("And", t.GetGenericArguments().Select(nt => nt.Name));
                                return $"{nestedPrefix}Of{nestedArgs}";
                            }

                            return t.Name;
                        }));
                        return $"{prefix}Of{genericArgs}";
                    }

                    // Handle non-generic types
                    return type.Name;
                });
            }
        );
    }

    private void ConfigureHealthChecks(IServiceCollection services)
    {
        services.AddHealthChecks()
            .AddCheck<HealthChecks.DocumentConversionHealthCheck>("document_conversion");
    }

    private void ConfigureLocalization(IServiceCollection services)
    {
        services.AddLocalization(options => options.ResourcesPath = "Resources");

        services.Configure<AbpLocalizationOptions>(options =>
        {
            options.Resources
                .Get<HotelFrontOfficeResource>()
                .AddBaseTypes(typeof(AbpValidationResource))
                .AddVirtualJson("/Localization/HotelFrontOffice");

            options.DefaultResourceType = typeof(HotelFrontOfficeResource);
        });
    }

    private void ConfigureHttpClients(IServiceCollection services, IConfiguration configuration)
    {
        services.AddHttpClient("IdentityServer", client =>
            {
                // Configure the client for the Identity Server
                var identityServerUrl = configuration["AuthServer:Authority"];
                if (!string.IsNullOrEmpty(identityServerUrl))
                {
                    client.BaseAddress = new Uri(identityServerUrl);
                }
                else
                {
                    throw new InvalidOperationException("AuthServer:Authority is not configured in appsettings.json");
                }

                // Skip SSL certificate validation in development
                if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata"))
                {
                    client.DefaultRequestHeaders.Add("X-Skip-SSL-Verification", "true");
                }

                // Set a reasonable timeout
                client.Timeout = TimeSpan.FromSeconds(30);
            })
            .ConfigurePrimaryHttpMessageHandler(() =>
            {
                return new HttpClientHandler
                {
                    // Skip SSL certificate validation in development
                    ServerCertificateCustomValidationCallback =
                        HttpClientHandler.DangerousAcceptAnyServerCertificateValidator
                };
            });

        // Add HTTP client for document conversion API
        services.AddHttpClient("DocumentConversion", client =>
            {
                // Configure the client for the document conversion API
                var apiUrl = configuration["DocumentConversion:ApiUrl"];
                if (!string.IsNullOrEmpty(apiUrl))
                {
                    var uri = new Uri(apiUrl);
                    client.BaseAddress = new Uri($"{uri.Scheme}://{uri.Host}:{uri.Port}");
                }

                // Set a longer timeout for document conversion (5 minutes)
                client.Timeout = TimeSpan.FromMinutes(5);
            })
            .ConfigurePrimaryHttpMessageHandler(() =>
            {
                return new HttpClientHandler
                {
                    // Skip SSL certificate validation for internal services
                    ServerCertificateCustomValidationCallback =
                        HttpClientHandler.DangerousAcceptAnyServerCertificateValidator
                };
            });
    }


    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        // Start background workers
        var backgroundWorkerManager = context.ServiceProvider.GetRequiredService<IBackgroundWorkerManager>();

        // Add temporary ZIP file cleanup worker
        backgroundWorkerManager.AddAsync(
            context.ServiceProvider.GetRequiredService<TemporaryZipFileCleanupWorker>()
        );

        // Add entity change tracking worker
        // backgroundWorkerManager.AddAsync(
        //     context.ServiceProvider.GetRequiredService<EntityChangeTrackingWorker>()
        // );

        app.UseForwardedHeaders();

        // Add our custom middleware to handle unsupported media type errors
        app.UseUnsupportedMediaTypeMiddleware();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        if (!env.IsDevelopment())
        {
            app.UseErrorPage();
            app.UseHsts();
        }

        app.UseApiAuthResponseMiddleware();

        app.UseCorrelationId();
        app.MapAbpStaticAssets();
        app.UseAbpStudioLink();
        app.UseRouting();
        app.UseCors(); // Enable CORS for SignalR
        app.UseAbpSecurityHeaders();

        // Add token endpoint proxy middleware to forward token requests to Identity Server
        app.UseMiddleware<TokenEndpointProxyMiddleware>();

        // Use optimized token validation middleware for better performance
        app.UseOptimizedTokenValidation();

        // Add our token expiration middleware to format 401 responses
        app.UseTokenExpirationMiddleware();

        // Configure authentication middleware
        app.UseAuthentication();

        // Use optimized user synchronization middleware for better performance
        app.UseOptimizedUserSynchronization();

        // Use OpenIddict validation
        app.UseAbpOpenIddictValidation();

        // Add our custom middleware to handle external users
        app.UseMiddleware<ExternalUserAuthenticationMiddleware>();

        // Add custom middleware to handle authentication errors
        app.Use(async (context, next) =>
        {
            try
            {
                await next();
            }
            catch (Exception ex)
            {
                // Check if this is an authentication error
                bool isAuthError =
                    ex.Message.Contains("Unable to obtain configuration") ||
                    ex.Message.Contains("IDX20803") ||
                    ex.Message.Contains("IDX") || // Any JWT token validation error
                    ex.Message.Contains("Bearer") ||
                    ex.Message.Contains("JWT") ||
                    ex.Message.Contains("token") ||
                    ex.Message.Contains("authentication") ||
                    ex.Message.Contains("No authenticationScheme") ||
                    (ex.InnerException != null && (
                        ex.InnerException.Message.Contains("IDX") ||
                        ex.InnerException.Message.Contains("Bearer") ||
                        ex.InnerException.Message.Contains("JWT") ||
                        ex.InnerException.Message.Contains("token") ||
                        ex.InnerException.Message.Contains("authentication") ||
                        ex.InnerException.Message.Contains("Unable to obtain configuration")
                    ));

                if (isAuthError)
                {
                    // Log the error but continue processing
                    Console.WriteLine($"Authentication error: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    }

                    // Remove the authorization header to bypass authentication
                    if (context.Request.Headers.ContainsKey("Authorization"))
                    {
                        context.Request.Headers.Remove("Authorization");
                    }

                    // Continue processing the request without authentication
                    await next();
                }
                else
                {
                    // Re-throw non-authentication errors
                    throw;
                }
            }
        });

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseDynamicClaims();

        // Add our custom middleware to handle authorization responses
        app.UseAuthorizationResponseMiddleware();

        app.UseAuthorization();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "HotelFrontOffice API");
        });

        // Use TickerQ
        app.UseTickerQ();

        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints(endpoints =>
        {
            endpoints.MapHealthChecks("/health", new HealthCheckOptions
            {
                Predicate = _ => true,
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });

            // SignalR Hub is automatically mapped by ABP at /signalr-hubs/room-status
        });
    }
}