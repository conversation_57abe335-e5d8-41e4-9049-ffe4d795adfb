namespace Imip.HotelFrontOffice.Reports;

public class ReportExcelColumnConfigDto
{
    public string ColumnName { get; set; } = default!;
    public ExcelCellType CellType { get; set; } = ExcelCellType.Text;
    public string NumberFormat { get; set; } = string.Empty; // Custom number format
    public ReportExcelStyleDto Style { get; set; } = default!;
    public int Width { get; set; } = 0; // 0 means auto-fit
}
