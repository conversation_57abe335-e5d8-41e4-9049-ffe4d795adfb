using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Imip.HotelFrontOffice.Reports;

public class ReportExcelHeaderDto
{

    public string Title { get; set; } = default!;
    public string SubTitle { get; set; } = default!;
    public List<ReportExcelHeaderRowDto> HeaderRows { get; set; } = new();
    public ReportExcelStyleDto TitleStyle { get; set; } = default!;
    public ReportExcelStyleDto SubTitleStyle { get; set; } = default!;
    public ReportExcelStyleDto HeaderStyle { get; set; } = default!;
    public ReportExcelStyleDto DataStyle { get; set; } = default!;
    public bool ShowGeneratedDate { get; set; } = true;
    public string DateFormat { get; set; } = "yyyy-MM-dd HH:mm:ss";
    public List<ReportExcelColumnConfigDto> ColumnConfigs { get; set; } = new();
}

public class ReportExcelHeaderRowDto
{
    public List<ReportExcelHeaderCellDto> Cells { get; set; } = new();
}

public class ReportExcelHeaderCellDto
{
    public string Text { get; set; } = default!;

    [JsonConverter(typeof(StringToIntConverter))]
    public int ColSpan { get; set; } = 1;

    [JsonConverter(typeof(StringToIntConverter))]
    public int RowSpan { get; set; } = 1;

    public ReportExcelStyleDto Style { get; set; } = default!;
}

public class ReportExcelStyleDto
{
    public string FontName { get; set; } = "Arial";
    public int FontSize { get; set; } = 10;
    public bool Bold { get; set; }
    public string BackgroundColor { get; set; } = default!; // Hex color
    public string FontColor { get; set; } = default!; // Hex color
    public string HorizontalAlignment { get; set; } = "Left"; // Left, Center, Right
    public string VerticalAlignment { get; set; } = "Top"; // Top, Middle, Bottom
    public bool Border { get; set; }
    public bool WrapText { get; set; }
}

/// <summary>
/// Custom JSON converter to handle string to int conversion for ColSpan and RowSpan
/// </summary>
public class StringToIntConverter : JsonConverter<int>
{
    public override int Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();
            if (string.IsNullOrEmpty(stringValue))
                return 1; // Default value for empty string

            if (int.TryParse(stringValue, out int result))
                return result;

            return 1; // Default value if parsing fails
        }

        if (reader.TokenType == JsonTokenType.Number)
        {
            return reader.GetInt32();
        }

        return 1; // Default value
    }

    public override void Write(Utf8JsonWriter writer, int value, JsonSerializerOptions options)
    {
        writer.WriteNumberValue(value);
    }
}
