using System;

namespace Imip.HotelFrontOffice.RoomStatuses;

/// <summary>
/// DTO for real-time room status change notifications via SignalR
/// </summary>
public class RoomStatusChangeNotificationDto
{
    /// <summary>
    /// The ID of the room whose status changed
    /// </summary>
    public Guid RoomId { get; set; }

    /// <summary>
    /// The room number for easy identification
    /// </summary>
    public string RoomNumber { get; set; } = default!;

    /// <summary>
    /// The room code for easy identification
    /// </summary>
    public string RoomCode { get; set; } = default!;

    /// <summary>
    /// The previous room status ID
    /// </summary>
    public Guid? PreviousStatusId { get; set; }

    /// <summary>
    /// The previous room status details
    /// </summary>
    public RoomStatusDto? PreviousStatus { get; set; }

    /// <summary>
    /// The new room status ID
    /// </summary>
    public Guid NewStatusId { get; set; }

    /// <summary>
    /// The new room status details
    /// </summary>
    public RoomStatusDto NewStatus { get; set; } = default!;

    /// <summary>
    /// The source of the status change (e.g., "Reservation Check IN", "Reservation Check OUT")
    /// </summary>
    public string StatusSource { get; set; } = default!;

    /// <summary>
    /// The timestamp when the status change occurred
    /// </summary>
    public DateTime ChangeTimestamp { get; set; }

    /// <summary>
    /// The ID of the user who triggered the status change
    /// </summary>
    public Guid? ChangedByUserId { get; set; }

    /// <summary>
    /// The name of the user who triggered the status change
    /// </summary>
    public string? ChangedByUserName { get; set; }

    /// <summary>
    /// Additional context about the status change (e.g., reservation ID)
    /// </summary>
    public string? ChangeContext { get; set; }
}
