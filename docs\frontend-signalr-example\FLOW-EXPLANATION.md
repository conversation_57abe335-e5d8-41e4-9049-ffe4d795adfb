# SignalR Room Status Update Flow (ABP Framework Standard)

## 🎯 **Two Main Approaches for Watching Room Changes**

### **Approach 1: Watch ALL Rooms (Recommended for Room List/Dashboard)**

**Use Case**: Room list page showing all rooms (like `/rooms` page)

**Flow**:
```
1. <PERSON>end connects to SignalR hub
2. <PERSON><PERSON> automatically adds user to "RoomStatusUpdates" group
3. ANY room status change triggers notification to ALL users in this group
4. Frontend receives notification and updates the specific room in the list
```

**Code Example**: `RoomListWithRealTimeUpdates.tsx`

**Advantages**:
- ✅ Simple setup - no need to manage individual room subscriptions
- ✅ Efficient for dashboards showing multiple rooms
- ✅ Automatically receives updates for all rooms
- ✅ No need to join/leave groups when rooms are added/removed

**When to Use**:
- Room list/dashboard pages
- Admin panels showing all rooms
- Reception desk displays

---

### **Approach 2: Watch SPECIFIC Rooms (For Detailed Views)**

**Use Case**: Single room detail page or monitoring specific rooms

**Flow**:
```
1. <PERSON>end connects to SignalR hub
2. <PERSON><PERSON> calls joinRoomGroup(roomId) for specific room
3. Only changes to THAT specific room trigger notifications
4. <PERSON>end receives targeted notifications for that room only
```

**Code Example**: `SingleRoomMonitor.tsx`

**Advantages**:
- ✅ Targeted notifications - only for rooms you care about
- ✅ Reduced network traffic for specific room views
- ✅ Better for detailed room monitoring
- ✅ Can track room-specific history

**When to Use**:
- Room detail pages
- Housekeeping staff monitoring assigned rooms
- Maintenance tracking specific rooms

---

## 🔄 **Complete Flow for Room List Dashboard**

### **Backend Flow**:
```
1. User performs check-in/check-out
2. ReservationDetailsAppService.HandleRoomStatusChangeAsync() called
3. UpdateRoomStatusAsync() updates room.RoomStatusId in database
4. SignalR notification sent to "RoomStatusUpdates" group
5. All connected clients receive the notification
```

### **Frontend Flow**:
```
1. Page loads → Fetch rooms via /api/app/rooms
2. SignalR connects → Automatically joins "RoomStatusUpdates" group
3. Display rooms in grid/list format
4. When notification received → Update specific room in state
5. UI automatically re-renders with new status
```

### **Key Code Points**:

#### **Backend (Already Implemented)**:
```csharp
// In RoomStatusHub.cs - Auto-join general group
await Groups.AddToGroupAsync(Context.ConnectionId, "RoomStatusUpdates");

// In ReservationDetailsAppService.cs - Send notification
await _roomStatusNotificationService.NotifyRoomStatusChangedAsync(notification);
```

#### **Frontend Implementation**:
```typescript
// 1. Fetch initial data
const fetchRooms = async () => {
  const response = await fetch(`${apiBaseUrl}/api/app/rooms`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  const data = await response.json();
  setRooms(data.items);
};

// 2. Handle real-time updates
const handleRoomStatusChanged = (notification) => {
  setRooms(prevRooms => 
    prevRooms.map(room => 
      room.id === notification.roomId 
        ? { ...room, roomStatus: notification.newStatus }
        : room
    )
  );
};

// 3. Set up SignalR listener
useEffect(() => {
  onRoomStatusChanged(handleRoomStatusChanged);
  return () => offRoomStatusChanged(handleRoomStatusChanged);
}, []);
```

---

## 📊 **What Data You Receive in Notifications**

When a room status changes, you receive:

```typescript
interface RoomStatusChangeNotification {
  roomId: string;              // "123e4567-e89b-12d3-a456-426614174000"
  roomNumber: string;          // "101"
  roomCode: string;            // "STD101"
  previousStatusId?: string;   // Previous status ID
  previousStatus?: {           // Previous status details
    id: string;
    name: string;              // "Vacant Ready"
    color: string;             // "#green"
    code: string;              // "VR"
  };
  newStatusId: string;         // New status ID
  newStatus: {                 // New status details
    id: string;
    name: string;              // "Occupied"
    color: string;             // "#red"
    code: string;              // "OVC"
  };
  statusSource: string;        // "Reservation Check IN"
  changeTimestamp: string;     // "2024-01-15T10:30:00Z"
  changedByUserId?: string;    // User who made the change
  changedByUserName?: string;  // "john.doe"
  changeContext?: string;      // Additional context
}
```

---

## 🚀 **Implementation Steps for Your Room List**

### **Step 1: Install Dependencies**
```bash
npm install @microsoft/signalr
```

### **Step 2: Set Up Authentication**
```typescript
const getAccessToken = async () => {
  // Return your JWT token here
  return localStorage.getItem('access_token') || 
         sessionStorage.getItem('access_token');
};
```

### **Step 3: Use the Component**
```typescript
import { RoomListWithRealTimeUpdates } from './components/RoomListWithRealTimeUpdates';

function RoomsPage() {
  return (
    <RoomListWithRealTimeUpdates
      apiBaseUrl="https://localhost:44357"  // Your ABP backend URL
      getAccessToken={getAccessToken}
    />
  );
}
```

### **Step 4: Test the Flow**
1. Open the room list page
2. Verify SignalR connection shows "Connected"
3. Perform check-in/check-out in your system
4. Watch the room status update automatically without page refresh

---

## 🔧 **Customization Options**

### **Filter Notifications by Room Type**:
```typescript
const handleRoomStatusChanged = (notification) => {
  // Only update if it's a standard room
  if (notification.roomCode.startsWith('STD')) {
    // Update room...
  }
};
```

### **Add Visual Feedback**:
```typescript
const handleRoomStatusChanged = (notification) => {
  // Update room
  setRooms(/* ... */);
  
  // Show toast notification
  toast.success(`Room ${notification.roomNumber} status updated!`);
  
  // Highlight the changed room temporarily
  setHighlightedRoom(notification.roomId);
  setTimeout(() => setHighlightedRoom(null), 3000);
};
```

### **Performance Optimization**:
```typescript
// Debounce rapid updates
const debouncedUpdate = useMemo(
  () => debounce(handleRoomStatusChanged, 100),
  []
);
```

---

## 🎯 **Summary**

**For your room list displaying all rooms via `/api/app/rooms`:**

1. ✅ **Use Approach 1** (Watch ALL rooms)
2. ✅ **Don't join individual room groups** - use the automatic "RoomStatusUpdates" group
3. ✅ **Update specific room in state** when notification received
4. ✅ **No need to pass room IDs** - the notification contains the room ID
5. ✅ **UI updates automatically** through React state changes

The flow is: **Fetch all rooms → Connect SignalR → Receive notifications → Update specific room → UI re-renders**
