import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';

export interface RoomStatusChangeNotification {
  roomId: string;
  roomNumber: string;
  roomCode: string;
  previousStatusId?: string;
  previousStatus?: {
    id: string;
    name: string;
    color: string;
    code: string;
  };
  newStatusId: string;
  newStatus: {
    id: string;
    name: string;
    color: string;
    code: string;
  };
  statusSource: string;
  changeTimestamp: string;
  changedByUserId?: string;
  changedByUserName?: string;
  changeContext?: string;
}

export class SignalRService {
  private connection: HubConnection | null = null;
  private isConnecting = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000; // 5 seconds

  constructor(
    private hubUrl: string,
    private getAccessToken: () => Promise<string | null>
  ) {}

  async connect(): Promise<void> {
    if (this.connection?.state === 'Connected' || this.isConnecting) {
      return;
    }

    this.isConnecting = true;

    try {
      const accessToken = await this.getAccessToken();
      
      this.connection = new HubConnectionBuilder()
        .withUrl(this.hubUrl, {
          accessTokenFactory: () => accessToken || '',
          withCredentials: true,
        })
        .withAutomaticReconnect({
          nextRetryDelayInMilliseconds: (retryContext) => {
            // Exponential backoff with jitter
            const delay = Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000);
            const jitter = Math.random() * 1000;
            return delay + jitter;
          }
        })
        .configureLogging(LogLevel.Information)
        .build();

      // Set up event handlers
      this.setupEventHandlers();

      await this.connection.start();
      console.log('SignalR Connected');
      
      this.reconnectAttempts = 0;
      this.isConnecting = false;

      // Join the general room status updates group
      await this.joinRoomStatusUpdatesGroup();

    } catch (error) {
      console.error('SignalR Connection Error:', error);
      this.isConnecting = false;
      
      // Attempt to reconnect
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        setTimeout(() => this.connect(), this.reconnectDelay);
      }
    }
  }

  async disconnect(): Promise<void> {
    if (this.connection) {
      await this.connection.stop();
      this.connection = null;
      console.log('SignalR Disconnected');
    }
  }

  private setupEventHandlers(): void {
    if (!this.connection) return;

    this.connection.onreconnecting((error) => {
      console.log('SignalR Reconnecting...', error);
    });

    this.connection.onreconnected((connectionId) => {
      console.log('SignalR Reconnected:', connectionId);
      // Rejoin groups after reconnection
      this.joinRoomStatusUpdatesGroup();
    });

    this.connection.onclose((error) => {
      console.log('SignalR Connection Closed:', error);
    });
  }

  private async joinRoomStatusUpdatesGroup(): Promise<void> {
    if (this.connection?.state === 'Connected') {
      try {
        // The hub automatically adds users to the "RoomStatusUpdates" group on connection
        console.log('Joined room status updates group');
      } catch (error) {
        console.error('Error joining room status updates group:', error);
      }
    }
  }

  async joinRoomGroup(roomId: string): Promise<void> {
    if (this.connection?.state === 'Connected') {
      try {
        await this.connection.invoke('JoinRoomGroup', roomId);
        console.log(`Joined room group for room ${roomId}`);
      } catch (error) {
        console.error(`Error joining room group for room ${roomId}:`, error);
      }
    }
  }

  async leaveRoomGroup(roomId: string): Promise<void> {
    if (this.connection?.state === 'Connected') {
      try {
        await this.connection.invoke('LeaveRoomGroup', roomId);
        console.log(`Left room group for room ${roomId}`);
      } catch (error) {
        console.error(`Error leaving room group for room ${roomId}:`, error);
      }
    }
  }

  async ping(): Promise<void> {
    if (this.connection?.state === 'Connected') {
      try {
        await this.connection.invoke('Ping');
      } catch (error) {
        console.error('Error pinging SignalR hub:', error);
      }
    }
  }

  onRoomStatusChanged(callback: (notification: RoomStatusChangeNotification) => void): void {
    if (this.connection) {
      this.connection.on('RoomStatusChanged', callback);
    }
  }

  onPong(callback: (timestamp: string) => void): void {
    if (this.connection) {
      this.connection.on('Pong', callback);
    }
  }

  offRoomStatusChanged(callback: (notification: RoomStatusChangeNotification) => void): void {
    if (this.connection) {
      this.connection.off('RoomStatusChanged', callback);
    }
  }

  offPong(callback: (timestamp: string) => void): void {
    if (this.connection) {
      this.connection.off('Pong', callback);
    }
  }

  get connectionState(): string {
    return this.connection?.state || 'Disconnected';
  }

  get isConnected(): boolean {
    return this.connection?.state === 'Connected';
  }
}
