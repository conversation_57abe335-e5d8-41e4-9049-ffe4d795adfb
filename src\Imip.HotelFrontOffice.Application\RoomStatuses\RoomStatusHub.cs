using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Volo.Abp.AspNetCore.SignalR;

namespace Imip.HotelFrontOffice.RoomStatuses;

/// <summary>
/// SignalR Hub for real-time room status updates
/// Following ABP Framework standards
/// </summary>
[Authorize]
[HubRoute("/signalr-hubs/room-status")]
public class RoomStatusHub : AbpHub
{
    // No need for constructor - ABP provides Logger, CurrentUser, etc. as base properties

    /// <summary>
    /// Called when a client connects to the hub
    /// </summary>
    /// <returns>A task representing the asynchronous operation</returns>
    public override async Task OnConnectedAsync()
    {
        var userId = CurrentUser.Id;
        var userName = CurrentUser.UserName;

        Logger.LogInformation("User {UserName} (ID: {UserId}) connected to RoomStatusHub with connection ID: {ConnectionId}",
            userName, userId, Context.ConnectionId);

        // Add user to a general room status updates group
        await Groups.AddToGroupAsync(Context.ConnectionId, "RoomStatusUpdates");

        // Optionally add user to role-based groups for targeted notifications
        if (CurrentUser.IsInRole("admin") || CurrentUser.IsInRole("frontdesk"))
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "FrontDeskStaff");
        }

        await base.OnConnectedAsync();
    }

    /// <summary>
    /// Called when a client disconnects from the hub
    /// </summary>
    /// <param name="exception">The exception that caused the disconnection, if any</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = CurrentUser.Id;
        var userName = CurrentUser.UserName;

        if (exception != null)
        {
            Logger.LogWarning(exception, "User {UserName} (ID: {UserId}) disconnected from RoomStatusHub with error. Connection ID: {ConnectionId}",
                userName, userId, Context.ConnectionId);
        }
        else
        {
            Logger.LogInformation("User {UserName} (ID: {UserId}) disconnected from RoomStatusHub. Connection ID: {ConnectionId}",
                userName, userId, Context.ConnectionId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Allows clients to join a specific room group for targeted notifications
    /// </summary>
    /// <param name="roomId">The room ID to subscribe to</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task JoinRoomGroup(string roomId)
    {
        if (string.IsNullOrWhiteSpace(roomId))
        {
            Logger.LogWarning("User {UserName} attempted to join room group with empty room ID", CurrentUser.UserName);
            return;
        }

        var groupName = $"Room_{roomId}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

        Logger.LogDebug("User {UserName} joined room group {GroupName}", CurrentUser.UserName, groupName);
    }

    /// <summary>
    /// Allows clients to leave a specific room group
    /// </summary>
    /// <param name="roomId">The room ID to unsubscribe from</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task LeaveRoomGroup(string roomId)
    {
        if (string.IsNullOrWhiteSpace(roomId))
        {
            Logger.LogWarning("User {UserName} attempted to leave room group with empty room ID", CurrentUser.UserName);
            return;
        }

        var groupName = $"Room_{roomId}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

        Logger.LogDebug("User {UserName} left room group {GroupName}", CurrentUser.UserName, groupName);
    }

    /// <summary>
    /// Ping method for connection health checks
    /// </summary>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task Ping()
    {
        await Clients.Caller.SendAsync("Pong", DateTime.UtcNow);
    }
}
