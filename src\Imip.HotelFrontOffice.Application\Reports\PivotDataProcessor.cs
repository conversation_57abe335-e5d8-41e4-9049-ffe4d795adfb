using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Reports;

/// <summary>
/// Service for processing data into pivot table format
/// </summary>
public class PivotDataProcessor : IPivotDataProcessor, ITransientDependency
{
    public async Task<PivotDataResult> TransformToPivotAsync(ReportPreviewDto sourceData, ReportPivotConfigDto pivotConfig)
    {
        var stopwatch = Stopwatch.StartNew();

        // Validate configuration
        var validation = await ValidatePivotConfigAsync(sourceData, pivotConfig);
        if (!validation.IsValid)
        {
            throw new ArgumentException($"Invalid pivot configuration: {string.Join(", ", validation.ErrorMessages)}");
        }

        // Generate dynamic columns
        var dynamicColumns = await GenerateDynamicColumnsAsync(sourceData, pivotConfig);

        // Create pivot data structure
        var pivotData = new ReportPreviewDto
        {
            ReportName = sourceData.ReportName + " (Pivot)",
            ExecutedAt = sourceData.ExecutedAt,
            Parameters = sourceData.Parameters,
            ExcelHeaderConfig = sourceData.ExcelHeaderConfig,
        };

        // Build column list: row groups + dynamic columns + totals column (if enabled)
        var allColumns = new List<string>();
        allColumns.AddRange(pivotConfig.RowGroupFields);
        allColumns.AddRange(dynamicColumns);
        if (pivotConfig.IncludeTotalsColumn)
        {
            allColumns.Add("Total");
        }
        pivotData.Columns = allColumns;

        // Group source data by row group fields
        var groupedData = GroupDataByRowFields(sourceData.Data, pivotConfig.RowGroupFields);

        // Transform each group into pivot row
        var pivotRows = new List<Dictionary<string, object>>();
        var columnMapping = CreateColumnMapping(dynamicColumns, pivotConfig);

        foreach (var group in groupedData)
        {
            var pivotRow = CreatePivotRow(group, pivotConfig, dynamicColumns);
            pivotRows.Add(pivotRow);
        }

        // Add totals row if enabled
        Dictionary<string, object>? totalsRow = null;
        if (pivotConfig.IncludeTotalsRow)
        {
            totalsRow = CreateTotalsRow(pivotRows, pivotConfig, dynamicColumns);
            pivotRows.Add(totalsRow);
        }

        pivotData.Data = pivotRows;
        pivotData.TotalRows = pivotRows.Count;

        stopwatch.Stop();

        return new PivotDataResult
        {
            PivotData = pivotData,
            DynamicColumns = dynamicColumns,
            RowGroupColumns = pivotConfig.RowGroupFields,
            ColumnMapping = columnMapping,
            TotalsRow = totalsRow,
            Metadata = new PivotMetadata
            {
                PivotColumnCount = dynamicColumns.Count,
                RowGroupCount = groupedData.Count,
                TotalDataPoints = sourceData.Data.Count,
                AggregationType = pivotConfig.AggregationType,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            }
        };
    }

    public async Task<List<string>> GenerateDynamicColumnsAsync(ReportPreviewDto sourceData, ReportPivotConfigDto pivotConfig)
    {
        await Task.CompletedTask; // Make async for future enhancements

        // Extract unique values from pivot column field
        var uniqueValues = sourceData.Data
            .Where(row => row.ContainsKey(pivotConfig.PivotColumnField))
            .Select(row => row[pivotConfig.PivotColumnField]?.ToString() ?? "")
            .Where(value => !string.IsNullOrEmpty(value))
            .Distinct()
            .ToList();

        // Apply formatting and sorting
        var formattedColumns = FormatColumnHeaders(uniqueValues, pivotConfig.HeaderFormat);
        var sortedColumns = SortColumns(formattedColumns, uniqueValues, pivotConfig.ColumnSorting);

        return sortedColumns;
    }

    public async Task<PivotValidationResult> ValidatePivotConfigAsync(ReportPreviewDto sourceData, ReportPivotConfigDto pivotConfig)
    {
        await Task.CompletedTask; // Make async for future enhancements

        var result = new PivotValidationResult { IsValid = true };

        // Check if pivot column field exists
        if (!sourceData.Columns.Contains(pivotConfig.PivotColumnField))
        {
            result.IsValid = false;
            result.ErrorMessages.Add($"Pivot column field '{pivotConfig.PivotColumnField}' not found in source data");
        }

        // Check if value field exists
        if (!sourceData.Columns.Contains(pivotConfig.ValueField))
        {
            result.IsValid = false;
            result.ErrorMessages.Add($"Value field '{pivotConfig.ValueField}' not found in source data");
        }

        // Check if row group fields exist
        foreach (var field in pivotConfig.RowGroupFields)
        {
            if (!sourceData.Columns.Contains(field))
            {
                result.IsValid = false;
                result.ErrorMessages.Add($"Row group field '{field}' not found in source data");
            }
        }

        // Check for potential performance issues
        var uniquePivotValues = sourceData.Data
            .Where(row => row.ContainsKey(pivotConfig.PivotColumnField))
            .Select(row => row[pivotConfig.PivotColumnField]?.ToString())
            .Distinct()
            .Count();

        if (uniquePivotValues > 100)
        {
            result.WarningMessages.Add($"Large number of pivot columns ({uniquePivotValues}). Consider filtering data or using different pivot field.");
        }

        if (sourceData.Data.Count > 10000)
        {
            result.WarningMessages.Add($"Large dataset ({sourceData.Data.Count} rows). Processing may take longer.");
        }

        return result;
    }

    private List<IGrouping<string, Dictionary<string, object>>> GroupDataByRowFields(
        List<Dictionary<string, object>> data,
        List<string> rowGroupFields)
    {
        return data
            .GroupBy(row => string.Join("|", rowGroupFields.Select(field =>
                row.ContainsKey(field) ? row[field]?.ToString() ?? "" : "")))
            .ToList();
    }

    private Dictionary<string, object> CreatePivotRow(
        IGrouping<string, Dictionary<string, object>> group,
        ReportPivotConfigDto pivotConfig,
        List<string> dynamicColumns)
    {
        var pivotRow = new Dictionary<string, object>();

        // Add row group values
        var firstRow = group.First();
        foreach (var field in pivotConfig.RowGroupFields)
        {
            pivotRow[field] = firstRow.ContainsKey(field) ? firstRow[field] : "";
        }

        // Initialize dynamic columns with default values
        foreach (var column in dynamicColumns)
        {
            pivotRow[column] = pivotConfig.DefaultValue ?? 0;
        }

        // Aggregate values for each pivot column
        foreach (var dataRow in group)
        {
            if (!dataRow.ContainsKey(pivotConfig.PivotColumnField) ||
                !dataRow.ContainsKey(pivotConfig.ValueField))
                continue;

            var pivotColumnValue = dataRow[pivotConfig.PivotColumnField]?.ToString() ?? "";
            var formattedColumnName = FormatSingleColumnHeader(pivotColumnValue, pivotConfig.HeaderFormat);

            if (dynamicColumns.Contains(formattedColumnName))
            {
                var currentValue = Convert.ToDecimal(pivotRow[formattedColumnName] ?? 0);
                var newValue = ParseNumericValue(dataRow[pivotConfig.ValueField]);

                pivotRow[formattedColumnName] = ApplyAggregation(currentValue, newValue, pivotConfig.AggregationType);
            }
        }

        // Add totals column if enabled
        if (pivotConfig.IncludeTotalsColumn)
        {
            var total = dynamicColumns.Sum(col => Convert.ToDecimal(pivotRow[col] ?? 0));
            pivotRow["Total"] = total;
        }

        return pivotRow;
    }

    private Dictionary<string, object> CreateTotalsRow(
        List<Dictionary<string, object>> pivotRows,
        ReportPivotConfigDto pivotConfig,
        List<string> dynamicColumns)
    {
        var totalsRow = new Dictionary<string, object>();

        // Set row group fields to "Total" or similar
        foreach (var field in pivotConfig.RowGroupFields)
        {
            totalsRow[field] = field == pivotConfig.RowGroupFields.First() ? "TOTAL" : "";
        }

        // Calculate totals for each dynamic column
        foreach (var column in dynamicColumns)
        {
            var total = pivotRows
                .Where(row => row.ContainsKey(column))
                .Sum(row => Convert.ToDecimal(row[column] ?? 0));
            totalsRow[column] = total;
        }

        // Add grand total if totals column is enabled
        if (pivotConfig.IncludeTotalsColumn)
        {
            var grandTotal = dynamicColumns.Sum(col => Convert.ToDecimal(totalsRow[col] ?? 0));
            totalsRow["Total"] = grandTotal;
        }

        return totalsRow;
    }

    private List<string> FormatColumnHeaders(List<string> values, PivotColumnHeaderFormat format)
    {
        return values.Select(value => FormatSingleColumnHeader(value, format)).ToList();
    }

    private string FormatSingleColumnHeader(string value, PivotColumnHeaderFormat format)
    {
        var formatted = value;

        // Apply custom mapping first
        if (format.CustomMapping.ContainsKey(value))
        {
            formatted = format.CustomMapping[value];
        }
        // Apply date formatting if specified
        else if (!string.IsNullOrEmpty(format.DateFormat) && DateTime.TryParse(value, out var dateValue))
        {
            formatted = dateValue.ToString(format.DateFormat);
        }

        // Apply prefix and suffix
        if (!string.IsNullOrEmpty(format.Prefix))
            formatted = format.Prefix + formatted;
        if (!string.IsNullOrEmpty(format.Suffix))
            formatted = formatted + format.Suffix;

        // Apply max length
        if (format.MaxLength.HasValue && formatted.Length > format.MaxLength.Value)
        {
            formatted = formatted.Substring(0, format.MaxLength.Value);
        }

        return formatted;
    }

    private List<string> SortColumns(List<string> formattedColumns, List<string> originalValues, PivotColumnSorting sorting)
    {
        if (sorting.Direction == PivotSortDirection.Custom && sorting.CustomOrder.Any())
        {
            return sorting.CustomOrder.Where(formattedColumns.Contains).ToList();
        }

        var paired = formattedColumns.Zip(originalValues, (formatted, original) => new { Formatted = formatted, Original = original }).ToList();

        var sorted = sorting.SortType switch
        {
            PivotSortType.Numerical => paired.OrderBy(p => decimal.TryParse(p.Original, out var num) ? num : 0).ToList(),
            PivotSortType.Date => paired.OrderBy(p => DateTime.TryParse(p.Original, out var date) ? date : DateTime.MinValue).ToList(),
            _ => paired.OrderBy(p => p.Formatted).ToList()
        };

        if (sorting.Direction == PivotSortDirection.Descending)
        {
            sorted.Reverse();
        }

        return sorted.Select(p => p.Formatted).ToList();
    }

    private Dictionary<string, string> CreateColumnMapping(List<string> dynamicColumns, ReportPivotConfigDto pivotConfig)
    {
        // This could be enhanced to provide more detailed mapping information
        return dynamicColumns.ToDictionary(col => col, col => col);
    }

    private decimal ParseNumericValue(object? value)
    {
        if (value == null) return 0;

        if (decimal.TryParse(value.ToString(), out var result))
            return result;

        return 0;
    }

    private decimal ApplyAggregation(decimal currentValue, decimal newValue, PivotAggregationType aggregationType)
    {
        return aggregationType switch
        {
            PivotAggregationType.Sum => currentValue + newValue,
            PivotAggregationType.Count => currentValue + 1,
            PivotAggregationType.Average => (currentValue + newValue) / 2, // Simplified - would need proper average calculation
            PivotAggregationType.Min => Math.Min(currentValue, newValue),
            PivotAggregationType.Max => Math.Max(currentValue, newValue),
            _ => currentValue + newValue
        };
    }
}
