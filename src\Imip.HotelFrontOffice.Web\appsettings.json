{"App": {"SelfUrl": "https://localhost:44357", "ServerRootAddress": "https://localhost:44357"}, "ConnectionStrings": {"Default": "Server=localhost;Database=fowisma_dev;User ID=sa;Password=******;Integrated Security=false;TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"}, "Seq": {"ServerUrl": "http://**********:5341", "ApiKey": "uELPnmIUMo5mkQDNN61T"}, "AuthServer": {"Authority": "https://api-identity-dev.imip.co.id", "RequireHttpsMetadata": false, "ClientId": "WismaBackendLocal", "ClientSecret": "O6pBjKEGh2jnANJXMIZS7n64BSw0VA7h", "CertificatePassPhrase": "08b57706-4619-42df-a584-e1852753ce9e"}, "StringEncryption": {"DefaultPassPhrase": "aikHRZS6ZDeP69vL"}, "UserSynchronization": {"IsEnabled": true, "UpdateExistingUsers": true, "SynchronizeRoles": true, "SynchronizeClaims": true, "EnableLogging": true}, "TickerQBasicAuth": {"Username": "admin", "Password": "admin"}, "DocumentConversion": {"ApiUrl": "http://**********:7000/forms/libreoffice/convert", "MaxConcurrentConversions": 2}, "PdfOptimization": {"EmbedFonts": false, "OptimizeIdenticalImages": true, "CompressionLevel": "Best", "MaxFileSizeWarningMB": 5.0, "MaxFileSizeForPostProcessingMB": 2.0, "UseMinimalFonts": true, "DefaultFontFamily": "<PERSON><PERSON>", "UseLighterFontWeight": false}, "Clock": {"Kind": "Local"}, "BlobStoring": {"Default": {"Type": "SFTP", "SFTP": {"Host": "**********", "Port": 22, "UserName": "ekbdev", "Password": "ekbdev#2024", "PrivateKeyPath": "", "PrivateKeyPassphrase": "", "BaseDirectory": "/ekb", "ConnectionTimeout": 30000, "OperationTimeout": 60000, "BufferSize": 4096, "CreateDirectoryIfNotExists": true}}}}