using System;
using System.Threading.Tasks;

namespace Imip.HotelFrontOffice.RoomStatuses;

/// <summary>
/// Service interface for sending real-time room status notifications via SignalR
/// </summary>
public interface IRoomStatusNotificationService
{
    /// <summary>
    /// Sends a room status change notification to all connected clients
    /// </summary>
    /// <param name="notification">The room status change notification</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task NotifyRoomStatusChangedAsync(RoomStatusChangeNotificationDto notification);

    /// <summary>
    /// Sends a room status change notification to specific users
    /// </summary>
    /// <param name="notification">The room status change notification</param>
    /// <param name="userIds">Array of user IDs to send the notification to</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task NotifyRoomStatusChangedToUsersAsync(RoomStatusChangeNotificationDto notification, params Guid[] userIds);

    /// <summary>
    /// Sends a room status change notification to users in specific groups
    /// </summary>
    /// <param name="notification">The room status change notification</param>
    /// <param name="groupNames">Array of group names to send the notification to</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task NotifyRoomStatusChangedToGroupsAsync(RoomStatusChangeNotificationDto notification, params string[] groupNames);
}
