using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.RoomStatuses;

/// <summary>
/// Service implementation for sending real-time room status notifications via SignalR
/// </summary>
public class RoomStatusNotificationService : IRoomStatusNotificationService, ITransientDependency
{
    private readonly IHubContext<RoomStatusHub> _hubContext;
    private readonly ILogger<RoomStatusNotificationService> _logger;

    public RoomStatusNotificationService(
        IHubContext<RoomStatusHub> hubContext,
        ILogger<RoomStatusNotificationService> logger)
    {
        _hubContext = hubContext;
        _logger = logger;
    }

    /// <summary>
    /// Sends a room status change notification to all connected clients
    /// </summary>
    /// <param name="notification">The room status change notification</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task NotifyRoomStatusChangedAsync(RoomStatusChangeNotificationDto notification)
    {
        try
        {
            _logger.LogInformation("Sending room status change notification for room {RoomNumber} (ID: {RoomId}) from {PreviousStatus} to {NewStatus}",
                notification.RoomNumber, notification.RoomId, 
                notification.PreviousStatus?.Name ?? "Unknown", notification.NewStatus.Name);

            // Send to all clients in the general room status updates group
            await _hubContext.Clients.Group("RoomStatusUpdates")
                .SendAsync("RoomStatusChanged", notification);

            // Send to specific room group if anyone is subscribed to that specific room
            var roomGroupName = $"Room_{notification.RoomId}";
            await _hubContext.Clients.Group(roomGroupName)
                .SendAsync("RoomStatusChanged", notification);

            _logger.LogDebug("Room status change notification sent successfully for room {RoomNumber}", notification.RoomNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send room status change notification for room {RoomNumber} (ID: {RoomId})",
                notification.RoomNumber, notification.RoomId);
            throw;
        }
    }

    /// <summary>
    /// Sends a room status change notification to specific users
    /// </summary>
    /// <param name="notification">The room status change notification</param>
    /// <param name="userIds">Array of user IDs to send the notification to</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task NotifyRoomStatusChangedToUsersAsync(RoomStatusChangeNotificationDto notification, params Guid[] userIds)
    {
        if (userIds == null || userIds.Length == 0)
        {
            _logger.LogWarning("No user IDs provided for targeted room status notification");
            return;
        }

        try
        {
            _logger.LogInformation("Sending room status change notification for room {RoomNumber} to {UserCount} specific users",
                notification.RoomNumber, userIds.Length);

            var userIdStrings = userIds.Select(id => id.ToString()).ToArray();
            await _hubContext.Clients.Users(userIdStrings)
                .SendAsync("RoomStatusChanged", notification);

            _logger.LogDebug("Room status change notification sent successfully to {UserCount} users for room {RoomNumber}",
                userIds.Length, notification.RoomNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send room status change notification to specific users for room {RoomNumber} (ID: {RoomId})",
                notification.RoomNumber, notification.RoomId);
            throw;
        }
    }

    /// <summary>
    /// Sends a room status change notification to users in specific groups
    /// </summary>
    /// <param name="notification">The room status change notification</param>
    /// <param name="groupNames">Array of group names to send the notification to</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task NotifyRoomStatusChangedToGroupsAsync(RoomStatusChangeNotificationDto notification, params string[] groupNames)
    {
        if (groupNames == null || groupNames.Length == 0)
        {
            _logger.LogWarning("No group names provided for targeted room status notification");
            return;
        }

        try
        {
            _logger.LogInformation("Sending room status change notification for room {RoomNumber} to {GroupCount} groups: {Groups}",
                notification.RoomNumber, groupNames.Length, string.Join(", ", groupNames));

            foreach (var groupName in groupNames)
            {
                await _hubContext.Clients.Group(groupName)
                    .SendAsync("RoomStatusChanged", notification);
            }

            _logger.LogDebug("Room status change notification sent successfully to {GroupCount} groups for room {RoomNumber}",
                groupNames.Length, notification.RoomNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send room status change notification to groups for room {RoomNumber} (ID: {RoomId})",
                notification.RoomNumber, notification.RoomId);
            throw;
        }
    }
}
