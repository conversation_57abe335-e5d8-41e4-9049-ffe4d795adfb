﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.HotelFrontOffice</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.HotelFrontOffice.Domain\Imip.HotelFrontOffice.Domain.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.Ddd.Domain" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Identity.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Account.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.AspNetCore.SignalR" Version="9.0.4" />
  </ItemGroup>

</Project>
