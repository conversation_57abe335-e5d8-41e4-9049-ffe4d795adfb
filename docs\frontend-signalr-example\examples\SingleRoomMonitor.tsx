import React, { useEffect, useState, useCallback } from 'react';
import { useSignalR } from '../hooks/useSignalR';
import { RoomStatusChangeNotification } from '../services/signalr-service';

interface Room {
  id: string;
  roomNumber: string;
  roomCode: string;
  roomStatus: {
    id: string;
    name: string;
    color: string;
    code: string;
  };
  lastModificationTime?: string;
  creationTime: string;
}

interface SingleRoomMonitorProps {
  roomId: string;
  apiBaseUrl: string;
  getAccessToken: () => Promise<string | null>;
}

export const SingleRoomMonitor: React.FC<SingleRoomMonitorProps> = ({
  roomId,
  apiBaseUrl,
  getAccessToken,
}) => {
  const [room, setRoom] = useState<Room | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusHistory, setStatusHistory] = useState<RoomStatusChangeNotification[]>([]);

  const signalRHubUrl = `${apiBaseUrl}/signalr-hubs/room-status`; // ABP standard hub route

  const {
    connectionState,
    isConnected,
    joinRoomGroup,
    leaveRoomGroup,
    onRoomStatusChanged,
    offRoomStatusChanged,
  } = useSignalR({
    hubUrl: signalRHubUrl,
    getAccessToken,
    autoConnect: true,
  });

  // Handle real-time room status changes for this specific room
  const handleRoomStatusChanged = useCallback((notification: RoomStatusChangeNotification) => {
    // Only process notifications for this specific room
    if (notification.roomId !== roomId) {
      return;
    }

    console.log('🔄 Room status changed for room:', notification);

    // Update the room data
    setRoom(prevRoom => {
      if (!prevRoom) return prevRoom;
      
      return {
        ...prevRoom,
        roomStatus: {
          id: notification.newStatus.id,
          name: notification.newStatus.name,
          color: notification.newStatus.color,
          code: notification.newStatus.code,
        },
        lastModificationTime: notification.changeTimestamp,
      };
    });

    // Add to status history (keep last 10)
    setStatusHistory(prevHistory => [
      notification,
      ...prevHistory.slice(0, 9)
    ]);

  }, [roomId]);

  // Set up SignalR event listeners
  useEffect(() => {
    onRoomStatusChanged(handleRoomStatusChanged);

    return () => {
      offRoomStatusChanged(handleRoomStatusChanged);
    };
  }, [handleRoomStatusChanged, onRoomStatusChanged, offRoomStatusChanged]);

  // Join/leave room-specific group when connected and roomId changes
  useEffect(() => {
    if (isConnected && roomId) {
      // Join the specific room group for targeted notifications
      joinRoomGroup(roomId);
      
      return () => {
        // Leave the room group when component unmounts or roomId changes
        leaveRoomGroup(roomId);
      };
    }
  }, [isConnected, roomId, joinRoomGroup, leaveRoomGroup]);

  // Fetch room data
  const fetchRoom = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = await getAccessToken();
      
      const response = await fetch(`${apiBaseUrl}/api/app/rooms/${roomId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch room: ${response.statusText}`);
      }

      const roomData: Room = await response.json();
      setRoom(roomData);
      
    } catch (err) {
      console.error('Error fetching room:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch room');
    } finally {
      setLoading(false);
    }
  }, [apiBaseUrl, getAccessToken, roomId]);

  // Initial data fetch
  useEffect(() => {
    if (roomId) {
      fetchRoom();
    }
  }, [fetchRoom, roomId]);

  const getStatusColor = (room: Room) => {
    return room.roomStatus?.color || '#gray';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading room...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <strong>Error:</strong> {error}
      </div>
    );
  }

  if (!room) {
    return (
      <div className="text-center py-8 text-gray-500">
        Room not found
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">
            Room {room.roomNumber}
          </h1>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            isConnected 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            SignalR: {connectionState}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Room Details */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Room Details</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Room Number</label>
              <div className="text-lg font-semibold text-gray-900">{room.roomNumber}</div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Room Code</label>
              <div className="text-lg text-gray-900">{room.roomCode}</div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Current Status</label>
              <div 
                className="inline-block px-4 py-2 rounded-lg text-white font-medium text-lg"
                style={{ backgroundColor: getStatusColor(room) }}
              >
                {room.roomStatus?.name || 'Unknown'}
              </div>
            </div>
            
            {room.lastModificationTime && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                <div className="text-gray-900">
                  {new Date(room.lastModificationTime).toLocaleString()}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Status History */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Status History</h2>
          
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {statusHistory.length === 0 ? (
              <div className="text-gray-500 text-center py-4">
                No status changes recorded yet
              </div>
            ) : (
              statusHistory.map((change, index) => (
                <div key={index} className="border-l-4 border-blue-500 pl-4 py-2">
                  <div className="flex items-center justify-between">
                    <div className="font-medium text-gray-900">
                      {change.previousStatus?.name || 'Unknown'} → {change.newStatus.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(change.changeTimestamp).toLocaleTimeString()}
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    {change.statusSource}
                  </div>
                  {change.changedByUserName && (
                    <div className="text-xs text-gray-500">
                      by {change.changedByUserName}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Real-time indicator */}
      {isConnected && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-3 py-2 rounded-full text-sm shadow-lg">
          🔄 Monitoring room {room.roomNumber}
        </div>
      )}
    </div>
  );
};
