using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.Reports;

public class Report : FullAuditedAggregateRoot<Guid>
{
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = default!;
    [StringLength(500)]
    public string? Description { get; set; }
    [Required]
    public ReportQueryType QueryType { get; set; }

    [Required]
    public string? Query { get; set; }
    public string? Parameters { get; set; } // JSON string of parameter definitions
    public string ExcelHeaderConfig { get; set; } // JSON string of ReportExcelHeaderDto
    public string? PivotConfig { get; set; } // JSON string of ReportPivotConfigDto
    public bool IsActive { get; set; } = true;
    public string? Roles { get; set; }

    protected Report()
    {
    }

    public Report(
        Guid id,
        string name,
        ReportQueryType queryType,
        string? description,
        string? query,
        string? parameters,
        string? roles,
        string? excelHeaderConfig = null,
        string? pivotConfig = null
    )
    {
        Id = id;
        Name = name;
        Description = description;
        QueryType = queryType;
        Query = query;
        Parameters = parameters ?? string.Empty;
        IsActive = true;
        Roles = roles;
        ExcelHeaderConfig = excelHeaderConfig ?? string.Empty;
        PivotConfig = pivotConfig;
    }
}
