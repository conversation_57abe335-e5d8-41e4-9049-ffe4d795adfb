using System;
using System.Collections.Generic;
using System.Linq;
using Imip.HotelFrontOffice.PaymentDetails;

namespace Imip.HotelFrontOffice.Documents.Invoice;

/// <summary>
/// Helper class for generating aligned tables for the Wisma invoice
/// </summary>
public static class AlignedTableGenerator
{
    /// <summary>
    /// Creates a consumption table with aligned rows for room charges, meal expenses, and other expenses
    /// </summary>
    public static TableGenerationDto CreateAlignedConsumptionTable(
        List<InvoicePaymentDetailDto> roomDetails,
        List<InvoicePaymentDetailDto> mealDetails,
        List<InvoicePaymentDetailDto> otherDetails,
        decimal roomTotal,
        decimal mealTotal,
        decimal otherTotal,
        Func<decimal, string> formatCurrency)
    {
        var tableData = new TableGenerationDto
        {
            Headers = [],
            Rows = [],
            // Enhanced table properties for better PDF conversion
            TableProperties = new TablePropertiesDto
            {
                Width = 5000,
                WidthType = "Pct",
                Layout = "Fixed",
                CellSpacing = 0,
                BorderSize = 2,  // Thicker borders for better visibility
                // Reduced cell margins for tighter layout
                DefaultCellLeftMargin = 36,   // ~0.025 inch (reduced from default 0.075 inch)
                DefaultCellRightMargin = 36,  // ~0.025 inch (reduced from default 0.075 inch)
                DefaultCellTopMargin = 0,
                DefaultCellBottomMargin = 0
            }
        };

        // Title row
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                Text = "附(Annex)：消费明细 (Consumption lists)",
                IsBold = true,
                HorizontalAlignment = TableCellAlignment.Center,
                ColSpan = 13,
                // Add explicit width for better PDF rendering
                WidthPercentage = 100
            }
            ]
        });

        // Create a completely different table structure that works better with PDF conversion
        // We'll use a fixed structure that matches exactly what we want in the PDF

        // First, add a row for the main title
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                new() {
                    Text = "附(Annex)：消费明细 (Consumption lists)",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Center,
                    ColSpan = 13,
                    WidthPercentage = 100
                }
            ]
        });

        // Create a row for the three main categories
        var categoryRow = new TableRowDto
        {
            Cells = []
        };

        // Room Charges header (38.5% total width)
        categoryRow.Cells.Add(new TableCellDto
        {
            Text = "客房费用\n(Room Charges)",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 38.5,
            ColSpan = 5
        });

        // Meal Expenses header (30.8% total width) - back to 4 columns
        categoryRow.Cells.Add(new TableCellDto
        {
            Text = "餐饮费用\n(Meal Expenses)",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 30.8,
            ColSpan = 4
        });

        // Others header (30.7% total width) - back to 4 columns
        categoryRow.Cells.Add(new TableCellDto
        {
            Text = "其他费用\n(Others)",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 30.7,
            ColSpan = 4
        });

        tableData.Rows.Add(categoryRow);

        // Column headers - create a fixed structure that matches exactly what we want
        var headerRow = new TableRowDto
        {
            Cells = []
        };

        // Room charges headers (5 columns) - Total 38.5%
        headerRow.Cells.Add(new TableCellDto
        {
            Text = "房型",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 8.5  // Wider for room type names
        });

        headerRow.Cells.Add(new TableCellDto
        {
            Text = "单价",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 7.5  // Price column
        });

        headerRow.Cells.Add(new TableCellDto
        {
            Text = "间数",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 6.0  // Narrow for quantity
        });

        headerRow.Cells.Add(new TableCellDto
        {
            Text = "晚数",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 6.0  // Narrow for nights
        });

        headerRow.Cells.Add(new TableCellDto
        {
            Text = "费用",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 10.5  // Wider for amount to prevent wrapping
        });

        // Meal expenses headers (4 columns) - Total 30.8%
        headerRow.Cells.Add(new TableCellDto
        {
            Text = "名称",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 9.0  // Wider for food names
        });

        headerRow.Cells.Add(new TableCellDto
        {
            Text = "数量",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 5.0  // Narrow for quantity
        });

        headerRow.Cells.Add(new TableCellDto
        {
            Text = "单价",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 7.8  // Price column
        });

        headerRow.Cells.Add(new TableCellDto
        {
            Text = "费用",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 9.0  // Wider for amount to prevent wrapping
        });

        // Other expenses headers (4 columns) - Total 30.7%
        headerRow.Cells.Add(new TableCellDto
        {
            Text = "名称",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 9.0  // Wider for service names
        });

        headerRow.Cells.Add(new TableCellDto
        {
            Text = "数量",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 5.0  // Narrow for quantity
        });

        headerRow.Cells.Add(new TableCellDto
        {
            Text = "单价",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 7.7  // Price column
        });

        headerRow.Cells.Add(new TableCellDto
        {
            Text = "费用",
            IsBold = true,
            HorizontalAlignment = TableCellAlignment.Center,
            WidthPercentage = 9.0  // Wider for amount to prevent wrapping
        });

        tableData.Rows.Add(headerRow);

        // Create unified structure for transaction date grouping
        // Get all unique transaction dates from both meal and other details, excluding null/MinValue dates
        var allTransactionDates = mealDetails
            .Concat(otherDetails)
            .Where(d => d.TransactionDate.HasValue && d.TransactionDate.Value != DateTime.MinValue)
            .Select(d => d.TransactionDate!.Value.Date)
            .Distinct()
            .OrderBy(d => d)
            .ToList();

        // Group meal and other details by transaction date
        var mealGroupsByDate = mealDetails
            .Where(d => d.TransactionDate.HasValue && d.TransactionDate.Value != DateTime.MinValue)
            .GroupBy(d => d.TransactionDate!.Value.Date)
            .ToDictionary(g => g.Key, g => g.ToList());

        var otherGroupsByDate = otherDetails
            .Where(d => d.TransactionDate.HasValue && d.TransactionDate.Value != DateTime.MinValue)
            .GroupBy(d => d.TransactionDate!.Value.Date)
            .ToDictionary(g => g.Key, g => g.ToList());

        // Handle items without transaction dates separately
        var mealItemsWithoutDate = mealDetails.Where(d => !d.TransactionDate.HasValue || d.TransactionDate.Value == DateTime.MinValue).ToList();
        var otherItemsWithoutDate = otherDetails.Where(d => !d.TransactionDate.HasValue || d.TransactionDate.Value == DateTime.MinValue).ToList();

        // Create unified row structure with transaction date subheaders
        var unifiedRows = new List<object>(); // Can be "DateSubheader", InvoicePaymentDetailDto, or null

        // First, add items without dates (if any)
        if (mealItemsWithoutDate.Any() || otherItemsWithoutDate.Any())
        {
            int maxItemsWithoutDate = Math.Max(mealItemsWithoutDate.Count, otherItemsWithoutDate.Count);
            for (int i = 0; i < maxItemsWithoutDate; i++)
            {
                var rowData = new
                {
                    MealItem = i < mealItemsWithoutDate.Count ? mealItemsWithoutDate[i] : null,
                    OtherItem = i < otherItemsWithoutDate.Count ? otherItemsWithoutDate[i] : null
                };
                unifiedRows.Add(rowData);
            }
        }

        // Then, add items grouped by transaction date
        foreach (var transactionDate in allTransactionDates)
        {
            // Add transaction date subheader row
            unifiedRows.Add($"DateSubheader:{transactionDate:M.d}");

            // Get items for this transaction date
            var mealItemsForDate = mealGroupsByDate.GetValueOrDefault(transactionDate, []);
            var otherItemsForDate = otherGroupsByDate.GetValueOrDefault(transactionDate, []);

            // Add item rows for this transaction date
            int maxItemsForDate = Math.Max(mealItemsForDate.Count, otherItemsForDate.Count);
            for (int i = 0; i < maxItemsForDate; i++)
            {
                var rowData = new
                {
                    MealItem = i < mealItemsForDate.Count ? mealItemsForDate[i] : null,
                    OtherItem = i < otherItemsForDate.Count ? otherItemsForDate[i] : null
                };
                unifiedRows.Add(rowData);
            }
        }

        // Calculate maximum rows needed (room details vs unified consumption rows)
        int maxRows = Math.Max(roomDetails.Count, unifiedRows.Count);

        // Create data rows with aligned items
        for (int i = 0; i < maxRows; i++)
        {
            var rowCells = new List<TableCellDto>();

            // Room charges cells (columns 1-5) - unchanged logic
            if (i < roomDetails.Count)
            {
                var detail = roomDetails[i];

                // Get room type name from the description or use default
                string roomTypeName = !string.IsNullOrEmpty(detail.Description)
                    ? detail.Description
                    : "Room";

                // Calculate price per night and nights
                int nights = detail.Qty > 0 ? (int)detail.Qty : 1;
                decimal pricePerNight = detail.Amount > 0 && nights > 0
                    ? detail.Amount / nights
                    : 0;

                // Add room charge cells
                rowCells.Add(new() { Text = roomTypeName, HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 8.5, FontSize = 8 });
                rowCells.Add(new() { Text = formatCurrency(pricePerNight), HorizontalAlignment = TableCellAlignment.Right, WidthPercentage = 7.5, FontSize = 8 });
                rowCells.Add(new() { Text = "1", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 6.0, FontSize = 8 });
                rowCells.Add(new() { Text = nights.ToString(), HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 6.0, FontSize = 8 });
                rowCells.Add(new() { Text = formatCurrency(detail.Amount), HorizontalAlignment = TableCellAlignment.Right, WidthPercentage = 10.5, FontSize = 8 });
            }
            else
            {
                // Empty cells for room charges with correct widths
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 8.5, FontSize = 8 });
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 7.5, FontSize = 8 });
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 6.0, FontSize = 8 });
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 6.0, FontSize = 8 });
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 10.5, FontSize = 8 });
            }

            // Handle unified consumption rows (meal and other expenses with transaction date subheaders)
            if (i < unifiedRows.Count)
            {
                var unifiedRow = unifiedRows[i];

                if (unifiedRow is string dateSubheader && dateSubheader.StartsWith("DateSubheader:"))
                {
                    // This is a transaction date subheader row
                    string dateText = dateSubheader.Substring("DateSubheader:".Length);

                    // Meal expenses subheader - only in the name column (first column)
                    rowCells.Add(new()
                    {
                        Text = $"{dateText}",
                        HorizontalAlignment = TableCellAlignment.Center,
                        WidthPercentage = 9.0,
                        FontSize = 8,
                        IsBold = true
                    });
                    // Empty cells for quantity, unit price, and amount columns
                    rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 5.0, FontSize = 8 });
                    rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 7.8, FontSize = 8 });
                    rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });

                    // Other expenses subheader - only in the name column (first column)
                    rowCells.Add(new()
                    {
                        Text = dateText,
                        HorizontalAlignment = TableCellAlignment.Center,
                        WidthPercentage = 9.0,
                        FontSize = 8,
                        IsBold = true
                    });
                    // Empty cells for quantity, unit price, and amount columns
                    rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 5.0, FontSize = 8 });
                    rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 7.7, FontSize = 8 });
                    rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });
                }
                else
                {
                    // This is a regular item row with meal and other items
                    var rowData = unifiedRow as dynamic;

                    // Meal expenses cells (4 columns)
                    if (rowData?.MealItem != null)
                    {
                        var mealDetail = rowData.MealItem as InvoicePaymentDetailDto;
                        string foodName = !string.IsNullOrEmpty(mealDetail?.Description)
                            ? mealDetail.Description
                            : "Food Item";

                        decimal qty = mealDetail?.Qty > 0 ? mealDetail.Qty : 1;
                        decimal pricePerUnit = mealDetail?.Amount > 0 && qty > 0
                            ? mealDetail.Amount / qty
                            : 0;

                        rowCells.Add(new() { Text = foodName, HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });
                        rowCells.Add(new() { Text = qty.ToString("0"), HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 5.0, FontSize = 8 });
                        rowCells.Add(new() { Text = formatCurrency(pricePerUnit), HorizontalAlignment = TableCellAlignment.Right, WidthPercentage = 7.8, FontSize = 8 });
                        rowCells.Add(new() { Text = formatCurrency(mealDetail?.Amount ?? 0), HorizontalAlignment = TableCellAlignment.Right, WidthPercentage = 9.0, FontSize = 8 });
                    }
                    else
                    {
                        // Empty meal cells
                        rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });
                        rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 5.0, FontSize = 8 });
                        rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 7.8, FontSize = 8 });
                        rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });
                    }

                    // Other expenses cells (4 columns)
                    if (rowData?.OtherItem != null)
                    {
                        var otherDetail = rowData.OtherItem as InvoicePaymentDetailDto;
                        string serviceName = !string.IsNullOrEmpty(otherDetail?.Description)
                            ? otherDetail.Description
                            : "Service Item";

                        decimal qty = otherDetail?.Qty > 0 ? otherDetail.Qty : 1;
                        decimal pricePerUnit = otherDetail?.Amount > 0 && qty > 0
                            ? otherDetail.Amount / qty
                            : 0;

                        rowCells.Add(new() { Text = serviceName, HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });
                        rowCells.Add(new() { Text = qty.ToString("0"), HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 5.0, FontSize = 8 });
                        rowCells.Add(new() { Text = formatCurrency(pricePerUnit), HorizontalAlignment = TableCellAlignment.Right, WidthPercentage = 7.7, FontSize = 8 });
                        rowCells.Add(new() { Text = formatCurrency(otherDetail?.Amount ?? 0), HorizontalAlignment = TableCellAlignment.Right, WidthPercentage = 9.0, FontSize = 8 });
                    }
                    else
                    {
                        // Empty other cells
                        rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });
                        rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 5.0, FontSize = 8 });
                        rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 7.7, FontSize = 8 });
                        rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });
                    }
                }
            }
            else
            {
                // Empty cells for both meal and other expenses
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 5.0, FontSize = 8 });
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 7.8, FontSize = 8 });
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });

                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 5.0, FontSize = 8 });
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 7.7, FontSize = 8 });
                rowCells.Add(new() { Text = "", HorizontalAlignment = TableCellAlignment.Center, WidthPercentage = 9.0, FontSize = 8 });
            }

            // Add the row to the table
            tableData.Rows.Add(new TableRowDto { Cells = rowCells });
        }

        // Add subtotal row that matches the example
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                // "小计：" label for room charges (spans first 4 columns)
                new() {
                    Text = "小计：",
                    HorizontalAlignment = TableCellAlignment.Right,
                    ColSpan = 4,
                    WidthPercentage = 28.0  // 8.5 + 7.5 + 6.0 + 6.0
                },

                // Room charges subtotal
                new() {
                    Text = formatCurrency(roomTotal),
                    HorizontalAlignment = TableCellAlignment.Right,
                    WidthPercentage = 10.5
                },

                // "小计：" label for meal expenses (spans first 3 columns)
                new() {
                    Text = "小计：",
                    HorizontalAlignment = TableCellAlignment.Right,
                    ColSpan = 3,
                    WidthPercentage = 21.8  // 9.0 + 5.0 + 7.8
                },

                // Meal expenses subtotal
                new() {
                    Text = formatCurrency(mealTotal),
                    HorizontalAlignment = TableCellAlignment.Right,
                    WidthPercentage = 9.0
                },

                // "小计：" label for other expenses (spans first 3 columns)
                new() {
                    Text = "小计：",
                    HorizontalAlignment = TableCellAlignment.Right,
                    ColSpan = 3,
                    WidthPercentage = 21.7  // 9.0 + 5.0 + 7.7
                },

                // Other expenses subtotal
                new() {
                    Text = formatCurrency(otherTotal),
                    HorizontalAlignment = TableCellAlignment.Right,
                    WidthPercentage = 9.0
                }
            ]
        });

        // Grand total row that matches the example
        tableData.Rows.Add(new TableRowDto
        {
            Cells =
            [
                // "总计 (Total)：" label
                new() {
                    Text = "总计 (Total)：",
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Right,
                    ColSpan = 5,
                    WidthPercentage = 38.5
                },

                // Grand total amount
                new() {
                    Text = formatCurrency(roomTotal + mealTotal + otherTotal),
                    IsBold = true,
                    HorizontalAlignment = TableCellAlignment.Right,
                    ColSpan = 8,
                    WidthPercentage = 61.5
                }
            ]
        });

        return tableData;
    }
}
