# Real-time Room Status Updates with SignalR

This implementation provides real-time room status updates using SignalR in your ABP Framework backend with a Next.js frontend dashboard.

## Backend Implementation (ABP Framework)

### 1. SignalR Hub and Services

The following files have been created/modified:

#### New Files Created:
- `src/Imip.HotelFrontOffice.Application.Contracts/RoomStatuses/RoomStatusChangeNotificationDto.cs`
- `src/Imip.HotelFrontOffice.Application.Contracts/RoomStatuses/IRoomStatusNotificationService.cs`
- `src/Imip.HotelFrontOffice.Application/RoomStatuses/RoomStatusHub.cs`
- `src/Imip.HotelFrontOffice.Application/RoomStatuses/RoomStatusNotificationService.cs`

#### Modified Files:
- `src/Imip.HotelFrontOffice.Application.Contracts/Imip.HotelFrontOffice.Application.Contracts.csproj` - Added SignalR package
- `src/Imip.HotelFrontOffice.Application/Imip.HotelFrontOffice.Application.csproj` - Added SignalR package
- `src/Imip.HotelFrontOffice.Application/ReservationDetails/ReservationDetailsAppService.cs` - Integrated SignalR notifications
- `src/Imip.HotelFrontOffice.Web/HotelFrontOfficeWebModule.cs` - Added SignalR configuration and hub mapping

### 2. Key Features

#### SignalR Hub (`RoomStatusHub`)
- **Authentication**: Requires user authentication
- **Group Management**: Automatically adds users to "RoomStatusUpdates" group
- **Role-based Groups**: Adds admin/frontdesk users to "FrontDeskStaff" group
- **Room-specific Groups**: Allows joining/leaving specific room groups
- **Connection Management**: Handles connect/disconnect events with logging
- **Health Check**: Provides ping/pong functionality

#### Notification Service (`RoomStatusNotificationService`)
- **Broadcast to All**: Send notifications to all connected clients
- **Targeted Notifications**: Send to specific users or groups
- **Error Handling**: Comprehensive error logging and handling
- **Multiple Delivery Methods**: Support for different notification targets

#### Integration with Business Logic
- **Automatic Notifications**: Room status changes trigger SignalR notifications
- **Rich Notification Data**: Includes previous/new status, user info, timestamps
- **Non-blocking**: SignalR failures don't disrupt main business operations
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

### 3. Configuration

#### CORS Configuration
```csharp
services.AddCors(options =>
{
    options.AddDefaultPolicy(builder =>
    {
        builder
            .WithOrigins("http://localhost:3000", "https://localhost:3000")
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials(); // Required for SignalR
    });
});
```

#### Hub Mapping
```csharp
endpoints.MapHub<RoomStatusHub>("/signalr/roomstatus");
```

## Frontend Implementation (Next.js)

### 1. SignalR Client Service

#### Features:
- **Automatic Reconnection**: Exponential backoff with jitter
- **Connection Management**: Connect/disconnect with state monitoring
- **Group Management**: Join/leave room-specific groups
- **Event Handling**: Type-safe event listeners
- **Error Handling**: Comprehensive error handling and logging
- **Health Monitoring**: Ping/pong functionality

### 2. React Hook (`useSignalR`)

#### Features:
- **State Management**: Connection state and status tracking
- **Auto-connect**: Automatic connection on component mount
- **Cleanup**: Proper cleanup on component unmount
- **Callback Management**: Easy event listener management

### 3. Dashboard Component (`RoomStatusDashboard`)

#### Features:
- **Real-time Updates**: Automatic UI updates when room status changes
- **Visual Indicators**: Color-coded room status display
- **Notification History**: Recent updates panel
- **Browser Notifications**: Native browser notifications (with permission)
- **Connection Status**: Visual connection state indicator
- **Manual Control**: Connect/disconnect buttons

## Installation and Setup

### Backend Setup

1. **Build the Solution**:
   ```bash
   dotnet build
   ```

2. **Run Database Migrations** (if needed):
   ```bash
   dotnet ef database update --project src/Imip.HotelFrontOffice.EntityFrameworkCore
   ```

3. **Start the Application**:
   ```bash
   dotnet run --project src/Imip.HotelFrontOffice.Web
   ```

### Frontend Setup

1. **Install Dependencies**:
   ```bash
   cd frontend-signalr-example
   npm install
   ```

2. **Configure API URL**:
   Update the `apiBaseUrl` in your component to match your backend URL:
   ```typescript
   const apiBaseUrl = "https://localhost:44357"; // Your ABP backend URL
   ```

3. **Implement Authentication**:
   Implement the `getAccessToken` function to return your JWT token:
   ```typescript
   const getAccessToken = async () => {
     // Return your JWT token here
     return localStorage.getItem('access_token');
   };
   ```

4. **Start Development Server**:
   ```bash
   npm run dev
   ```

## Usage

### Testing Real-time Updates

1. **Open the Dashboard**: Navigate to your Next.js application
2. **Verify Connection**: Check that SignalR shows "Connected" status
3. **Trigger Status Changes**: Use your existing reservation management to:
   - Check-in a guest (should change room status to "OVC IN Stay")
   - Check-out a guest (should change room status to "Dirty")
4. **Observe Real-time Updates**: The dashboard should automatically update without refresh

### API Endpoints

The SignalR hub is available at:
```
https://your-backend-url/signalr/roomstatus
```

### Hub Methods

- `JoinRoomGroup(roomId)`: Subscribe to updates for a specific room
- `LeaveRoomGroup(roomId)`: Unsubscribe from room updates
- `Ping()`: Health check method

### Hub Events

- `RoomStatusChanged`: Fired when any room status changes
- `Pong`: Response to ping with timestamp

## Security Considerations

1. **Authentication**: All SignalR connections require valid JWT tokens
2. **Authorization**: Hub methods check user permissions
3. **CORS**: Properly configured for your frontend domains
4. **Rate Limiting**: Consider implementing rate limiting for production

## Monitoring and Debugging

### Logging

The implementation includes comprehensive logging:
- Connection events (connect/disconnect)
- Group join/leave operations
- Notification sending success/failure
- Error conditions

### Browser Developer Tools

Monitor SignalR activity in browser console:
- Connection status changes
- Received notifications
- Error messages

### Server Logs

Check server logs for:
- SignalR connection events
- Notification sending attempts
- Authentication issues

## Production Considerations

1. **Scaling**: Consider using Redis backplane for multiple server instances
2. **Performance**: Monitor connection counts and message throughput
3. **Error Handling**: Implement proper error boundaries in React components
4. **Monitoring**: Set up application monitoring for SignalR metrics
5. **Security**: Review and harden CORS policies for production domains

## Troubleshooting

### Common Issues

1. **Connection Fails**: Check CORS configuration and authentication tokens
2. **No Notifications**: Verify hub mapping and event listener setup
3. **Authentication Errors**: Ensure JWT tokens are valid and properly formatted
4. **CORS Errors**: Update CORS policy to include your frontend domain

### Debug Steps

1. Check browser console for SignalR connection errors
2. Verify backend logs for authentication issues
3. Test hub methods using browser developer tools
4. Confirm room status changes are triggering notifications in backend logs

This implementation provides a robust, production-ready real-time room status update system that integrates seamlessly with your existing ABP Framework application.
